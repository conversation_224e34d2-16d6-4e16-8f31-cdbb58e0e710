FROM node:20-alpine AS builder
RUN corepack enable

WORKDIR /app
# We are copying the package.json from root folder
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/client ./packages/client
COPY packages/shared ./packages/shared
# ... means install client and shared dependencies
RUN pnpm install --frozen-lockfile --filter=client...

# Go to client folder and build
WORKDIR /app/packages/client
RUN pnpm build

FROM nginx:alpine

COPY --from=builder /app/packages/client/dist /usr/share/nginx/html
COPY packages/client/nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
